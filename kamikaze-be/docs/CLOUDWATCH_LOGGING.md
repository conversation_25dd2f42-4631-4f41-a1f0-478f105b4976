# CloudWatch Logging Configuration

This document describes the CloudWatch logging setup for Kamikaze AI Backend, which replaces local file logging with centralized cloud-based log management.

## Overview

The application has been configured to use AWS CloudWatch Logs for centralized logging instead of creating local log files. This provides:

- **Centralized Log Management**: All logs are stored in AWS CloudWatch
- **No Local Storage**: Container filesystem remains clean with no log files
- **Structured Logging**: JSON-formatted logs for better parsing and filtering
- **Automatic Retention**: CloudWatch handles log retention policies
- **Real-time Monitoring**: Logs are available immediately in CloudWatch console
- **Cost Optimization**: Pay only for logs stored and queried

## Configuration

### Environment Variables

The following environment variables control CloudWatch logging:

```bash
# Enable CloudWatch logging (default: true in production)
USE_CLOUDWATCH_LOGS=true

# Disable local file logging (default: false in production)
ENABLE_FILE_LOGGING=false

# Enable structured JSON logging (default: true)
STRUCTURED_LOGS=true

# Log level (default: INFO)
LOG_LEVEL=INFO

# AWS region for CloudWatch (default: us-east-1)
AWS_REGION=us-east-1
```

### CloudWatch Log Group

**Log Group Name**: `/aws/kamikaze-ai/backend`
**Retention Period**: 30 days (configurable)
**Region**: us-east-1 (or your configured AWS region)

## Setup Instructions

### 1. Create CloudWatch Log Group

Run the setup script to create the necessary CloudWatch resources:

```bash
# Make the script executable
chmod +x scripts/setup-cloudwatch-logs.sh

# Create log groups and set retention policy
./scripts/setup-cloudwatch-logs.sh setup

# View log group information
./scripts/setup-cloudwatch-logs.sh info
```

### 2. Configure AWS Credentials

Ensure your container has proper AWS credentials configured:

**Option A: IAM Role (Recommended for EC2/ECS)**
- Attach an IAM role with CloudWatch Logs permissions to your EC2 instance
- No additional configuration needed

**Option B: Environment Variables**
```bash
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_REGION=us-east-1
```

**Option C: AWS Credentials File**
- Mount AWS credentials file to container
- Not recommended for production

### 3. Required IAM Permissions

The application needs the following CloudWatch Logs permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents",
                "logs:DescribeLogGroups",
                "logs:DescribeLogStreams"
            ],
            "Resource": [
                "arn:aws:logs:*:*:log-group:/aws/kamikaze-ai/*",
                "arn:aws:logs:*:*:log-group:/aws/kamikaze-ai/*:*"
            ]
        }
    ]
}
```

## Log Format

### Structured JSON Logs

When `STRUCTURED_LOGS=true`, logs are formatted as JSON:

```json
{
    "timestamp": "2025-08-29T23:30:00.000Z",
    "level": "INFO",
    "logger": "kamikaze-ai",
    "message": "Application started successfully",
    "service": "kamikaze-ai-backend",
    "environment": "production",
    "hostname": "kamikaze-ai-container",
    "module": "main",
    "function": "start_application",
    "line": 42
}
```

### Simple Text Logs

When `STRUCTURED_LOGS=false`, logs use simple text format:

```
2025-08-29 23:30:00 - kamikaze-ai - INFO - Application started successfully
```

## Monitoring and Alerting

### CloudWatch Console

Access your logs in the AWS CloudWatch console:
```
https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/%2Faws%2Fkamikaze-ai%2Fbackend
```

### Log Filtering

Use CloudWatch Logs Insights to query your logs:

```sql
# Find all error logs
fields @timestamp, level, message
| filter level = "ERROR"
| sort @timestamp desc

# Find logs from specific module
fields @timestamp, module, message
| filter module = "trading"
| sort @timestamp desc

# Find logs with specific message pattern
fields @timestamp, message
| filter message like /database connection/
| sort @timestamp desc
```

### CloudWatch Alarms

Set up alarms for critical events:

```bash
# Create alarm for error rate
aws cloudwatch put-metric-alarm \
    --alarm-name "KamikazeAI-HighErrorRate" \
    --alarm-description "High error rate in Kamikaze AI" \
    --metric-name "ErrorCount" \
    --namespace "AWS/Logs" \
    --statistic "Sum" \
    --period 300 \
    --threshold 10 \
    --comparison-operator "GreaterThanThreshold"
```

## Development vs Production

### Development Mode
- Local file logging enabled for debugging
- Console logging always enabled
- Logs stored in `logs/` directory
- Simple text format for readability

### Production Mode
- CloudWatch logging only
- No local files created
- Structured JSON format
- Automatic log retention

## Troubleshooting

### Common Issues

**1. Logs not appearing in CloudWatch**
- Check AWS credentials and permissions
- Verify log group exists
- Check container stdout/stderr output

**2. Permission denied errors**
- Verify IAM permissions for CloudWatch Logs
- Check AWS region configuration

**3. High CloudWatch costs**
- Review log retention settings
- Filter unnecessary debug logs
- Use log sampling for high-volume logs

### Debug Commands

```bash
# Check if logs are being written to stdout
docker logs kamikaze-ai

# Test CloudWatch connectivity
aws logs describe-log-groups --region us-east-1

# View recent log events
aws logs get-log-events \
    --log-group-name "/aws/kamikaze-ai/backend" \
    --log-stream-name "application-$(date +%Y%m%d)" \
    --region us-east-1
```

## Migration from File Logging

If migrating from file-based logging:

1. **Backup existing logs** (if needed)
2. **Set environment variables** for CloudWatch
3. **Deploy updated application**
4. **Verify logs in CloudWatch**
5. **Remove old log files** from containers

The application automatically detects CloudWatch configuration and disables file logging when appropriate.

## Cost Optimization

- **Log Retention**: Set appropriate retention periods (30 days default)
- **Log Filtering**: Filter out debug logs in production
- **Log Sampling**: Sample high-frequency logs if needed
- **Regional Storage**: Use appropriate AWS region for cost optimization

## Support

For issues with CloudWatch logging:
1. Check the troubleshooting section above
2. Review AWS CloudWatch Logs documentation
3. Verify IAM permissions and AWS credentials
4. Check application logs for CloudWatch-related errors

name: CI/CD Pipeline

on:
  push:
    branches: [ cicd ]
  pull_request:
    branches: [ cicd ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Build and validation job - runs code quality checks and dependency installation
  build-and-validate:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python 3.12
      uses: actions/setup-python@v4
      with:
        python-version: 3.12

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-3.12-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-3.12-
          ${{ runner.os }}-pip-

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential wget
        # Install TA-Lib system library
        wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
        tar -xzf ta-lib-0.4.0-src.tar.gz
        cd ta-lib/
        ./configure --prefix=/usr
        make
        sudo make install
        cd ..

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        # Install numpy first (required for TA-Lib)
        pip install numpy
        # Then install project dependencies
        pip install -r requirements.txt

    - name: Code formatting check
      run: |
        echo "🎨 Checking code formatting with Black..."
        black --check --diff --color . || echo "⚠️ Code formatting issues found (non-blocking)"

    - name: Import sorting check
      run: |
        echo "📦 Checking import sorting with isort..."
        isort --check-only --diff --color . || echo "⚠️ Import sorting issues found (non-blocking)"

    - name: Linting
      run: |
        echo "🔍 Running Flake8 linting..."
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics || echo "⚠️ Critical linting issues found (non-blocking)"
        flake8 . --count --exit-zero --max-complexity=15 --max-line-length=88 --statistics

    - name: Validate project structure
      run: |
        echo "📁 Validating project structure..."
        test -f "app.py" || (echo "❌ app.py missing" && exit 1)
        test -f "requirements.txt" || (echo "❌ requirements.txt missing" && exit 1)
        test -f "Dockerfile" || (echo "❌ Dockerfile missing" && exit 1)
        test -d "src" || (echo "❌ src directory missing" && exit 1)
        echo "✅ Project structure validated"

    - name: Validate configuration
      run: |
        echo "⚙️ Validating configuration files..."
        if [ -f "config.json" ]; then
          python -c "import json; config = json.load(open('config.json')); print('✅ Config loaded successfully')"
        else
          echo "⚠️ config.json not found, skipping validation"
        fi
        echo "✅ Configuration validation completed"

  # Docker build job - builds and tests Docker images
  docker-build:
    runs-on: ubuntu-latest
    needs: build-and-validate
    if: github.event_name == 'push'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      run: |
        echo "🐳 Building Docker image..."
        docker build -t kamikaze-ai:${{ github.sha }} .
        docker build -t kamikaze-ai:latest .

    - name: Test Docker image
      run: |
        echo "🧪 Testing Docker image..."
        docker run --rm kamikaze-ai:latest python --version
        docker run --rm kamikaze-ai:latest python -c "import json; print('✅ Python works')"

    - name: Save Docker image
      if: github.ref == 'refs/heads/cicd'
      run: |
        echo "💾 Saving Docker image..."
        docker save kamikaze-ai:latest | gzip > kamikaze-ai-latest.tar.gz

    - name: Upload Docker image artifact
      if: github.ref == 'refs/heads/cicd'
      uses: actions/upload-artifact@v4
      with:
        name: docker-image
        path: kamikaze-ai-latest.tar.gz
        retention-days: 1
        
  # Staging deployment job - deploys to staging environment
  deploy-staging:
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/cicd'
    environment: development

    steps:
    - name: Download Docker image artifact
      uses: actions/download-artifact@v4
      with:
        name: docker-image
        path: .

    - name: Load Docker image
      run: docker load < kamikaze-ai-latest.tar.gz

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Tag and push Docker image
      run: |
        echo "🐳 Tagging and pushing Docker image for staging..."
        IMAGE_NAME_LOWER=$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]')
        if [ "${{ github.ref }}" = "refs/heads/cicd" ]; then
          BRANCH_TAG="staging"
        else
          BRANCH_TAG="dev-staging"
        fi
        docker tag kamikaze-ai:latest ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:${BRANCH_TAG}
        docker tag kamikaze-ai:latest ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:${{ github.sha }}
        docker push ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:${BRANCH_TAG}
        docker push ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:${{ github.sha }}

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."

        # Convert repository name to lowercase for Docker registry
        IMAGE_NAME_LOWER=$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]')

        # Determine deployment environment based on branch
        if [ "${{ github.ref }}" = "refs/heads/cicd" ]; then
          BRANCH_TAG="staging"
          ENV_NAME="staging"
        else
          BRANCH_TAG="dev-staging"
          ENV_NAME="dev-staging"
        fi

        echo "Branch: ${{ github.ref_name }}"
        echo "Environment: ${ENV_NAME}"
        echo "Image: ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:${BRANCH_TAG}"
        echo "Commit: ${{ github.sha }}"

        # Simulate deployment (replace with actual deployment commands)
        echo "✅ ${ENV_NAME} deployment completed"

        # Health check simulation
        echo "🏥 Running health checks..."
        sleep 5

  deploy-production:
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/cicd'
    environment: development

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Pull staging image and tag for production
      run: |
        IMAGE_NAME_LOWER=$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]')
        docker pull ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:staging
        docker tag ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:staging ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:latest
        docker tag ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:staging ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:prod-${{ github.sha }}
        docker push ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:latest
        docker push ${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}:prod-${{ github.sha }}

    - name: Setup SSH Agent
      uses: webfactory/ssh-agent@v0.9.0
      with:
        ssh-private-key: ${{ secrets.EC2_SSH_PRIVATE_KEY }}

    - name: Copy verification script to EC2
      run: |
        scp -i ~/.ssh/id_rsa -o StrictHostKeyChecking=no scripts/verify-deployment.sh ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }}:~/verify-deployment.sh

    - name: Validate GitHub Secrets
      run: |
        echo "Validating GitHub secrets configuration..."
        if [ -z "${{ secrets.AWS_ACCESS_KEY_ID }}" ]; then
          echo "ERROR: AWS_ACCESS_KEY_ID secret not configured in development environment"
          exit 1
        fi
        if [ -z "${{ secrets.AWS_SECRET_ACCESS_KEY }}" ]; then
          echo "ERROR: AWS_SECRET_ACCESS_KEY secret not configured in development environment"
          exit 1
        fi
        if [ -z "${{ secrets.AWS_REGION }}" ]; then
          echo "ERROR: AWS_REGION secret not configured in development environment"
          exit 1
        fi
        if [ -z "${{ secrets.DB_HOST }}" ]; then
          echo "ERROR: DB_HOST secret not configured in development environment"
          exit 1
        fi
        if [ -z "${{ secrets.DB_PASSWORD }}" ]; then
          echo "ERROR: DB_PASSWORD secret not configured in development environment"
          exit 1
        fi
        echo "✅ All required GitHub secrets are configured in development environment"

    - name: Deploy and Verify
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: ${{ secrets.AWS_REGION }}
        DB_HOST: ${{ secrets.DB_HOST }}
        DB_PORT: ${{ secrets.DB_PORT }}
        DB_NAME: ${{ secrets.DB_NAME }}
        DB_USER: ${{ secrets.DB_USER }}
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
        # Fallback database name for PostgreSQL default
        POSTGRES_DEFAULT_DB: postgres
        # Docker image reference
        DOCKER_IMAGE: ${{ env.REGISTRY }}/$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]'):latest
      run: |
        ssh -o StrictHostKeyChecking=no ${{ secrets.EC2_USER }}@${{ secrets.EC2_HOST }} << EOF
          set -ex

          echo "Starting deployment script..."

          echo "Environment variables received (sensitive values masked - showing only last 2 digits):"
          echo "AWS_REGION: ${AWS_REGION}"
          echo "DB_HOST: ${DB_HOST}"
          echo "DB_PORT: ${DB_PORT}"
          echo "DB_NAME: ${DB_NAME}"
          echo "DB_USER: ${DB_USER}"
          echo "AWS_ACCESS_KEY_ID: ***$(echo ${AWS_ACCESS_KEY_ID} | tail -c 3)"
          echo "AWS_SECRET_ACCESS_KEY: ***$(echo ${AWS_SECRET_ACCESS_KEY} | tail -c 3)"
          echo "DB_PASSWORD: ***$(echo ${DB_PASSWORD} | tail -c 3)"

          # Aggressive cleanup: find and kill any process on port 8000
          PIDS=$(sudo ss -lptn 'sport = :8000' | grep -oP 'pid=\K\d+(?=,)' || true)
          if [ -n "$PIDS" ]; then
            echo "Port 8000 is in use by the following PIDs, killing them..."
            echo "$PIDS" | while IFS= read -r pid; do
              if [ -n "$pid" ]; then
                echo "Killing process with PID: $pid"
                sudo kill -9 "$pid"
              fi
            done
            sleep 2
          fi
          
          # Fallback: Explicitly stop and remove the container if it exists
          docker stop kamikaze-ai || true
          docker rm kamikaze-ai || true

          # Restart Docker to clear any ghost processes or stuck states
          echo "Restarting Docker service..."
          sudo systemctl restart docker
          sleep 5

          # Final check to ensure port is free
          if sudo ss -lptn 'sport = :8000' | grep 'LISTEN'; then
            echo "ERROR: Port 8000 is still in use after Docker restart. Aborting."
            exit 1
          fi
          
          echo "Cleaning up unused Docker images..."
          docker system prune -af

          echo "Pulling latest image..."
          echo "Docker image: ${DOCKER_IMAGE}"
          docker pull ${DOCKER_IMAGE}
          
          echo "Running new container with full environment configuration..."
          docker run -d --name kamikaze-ai --restart always -p 8000:8000 \
            -e AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID}" \
            -e AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY}" \
            -e AWS_REGION="${AWS_REGION}" \
            -e AWS_DEFAULT_REGION="${AWS_REGION}" \
            -e USE_AWS_SECRETS=true \
            -e ENVIRONMENT=production \
            -e DB_HOST="${DB_HOST}" \
            -e DB_PORT="${DB_PORT}" \
            -e DB_NAME="${DB_NAME:-postgres}" \
            -e DB_USER="${DB_USER}" \
            -e DB_PASSWORD="${DB_PASSWORD}" \
            -e DB_SSL_MODE=require \
            -e LOG_LEVEL=INFO \
            -e ENABLE_FILE_LOGGING=false \
            -e USE_CLOUDWATCH_LOGS=true \
            -e STRUCTURED_LOGS=true \
            ${DOCKER_IMAGE}

          echo "Verifying deployment..."
          sleep 15
          docker ps | grep kamikaze-ai

          echo "Running health check..."
          curl -f http://localhost:8000/health || (echo "Health check failed" && docker logs kamikaze-ai && exit 1)

          echo "Running comprehensive deployment verification..."
          chmod +x ~/verify-deployment.sh
          ~/verify-deployment.sh || echo "⚠️ Deployment verification completed with warnings"

          echo "Deployment successful."
        EOF

    - name: Create release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        release_name: Release v${{ github.run_number }}
        body: |
          ## Release v${{ github.run_number }}

          **Commit:** ${{ github.sha }}
          **Branch:** ${{ github.ref_name }}

          ### Changes
          - Automated release from main branch
          - Docker image: `${{ env.REGISTRY }}/$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]'):staging`

          ### Deployment Status
          - ✅ Staging: Deployed successfully
          - ✅ Production: Deployed successfully to EC2
        draft: false
        prerelease: false

  # Rollback job - handles deployment failures
  rollback:
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure')
    environment: development

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Determine rollback target
      id: rollback-target
      run: |
        echo "🔍 Determining rollback target..."

        # Convert repository name to lowercase for Docker registry
        IMAGE_NAME_LOWER=$(echo "${{ env.IMAGE_NAME }}" | tr '[:upper:]' '[:lower:]')
        FULL_IMAGE_NAME="${{ env.REGISTRY }}/${IMAGE_NAME_LOWER}"

        # Determine which deployment failed and set rollback strategy
        if [ "${{ needs.deploy-production.result }}" = "failure" ]; then
          echo "❌ Production deployment failed"
          echo "target=production" >> $GITHUB_OUTPUT
          echo "image=${FULL_IMAGE_NAME}:staging" >> $GITHUB_OUTPUT
          echo "environment=production" >> $GITHUB_OUTPUT
        elif [ "${{ needs.deploy-staging.result }}" = "failure" ]; then
          echo "❌ Staging deployment failed"
          echo "target=staging" >> $GITHUB_OUTPUT
          echo "image=${FULL_IMAGE_NAME}:latest" >> $GITHUB_OUTPUT
          echo "environment=staging" >> $GITHUB_OUTPUT
        fi

    - name: Execute rollback
      run: |
        echo "🔄 Initiating rollback procedure for ${{ steps.rollback-target.outputs.target }}..."
        echo "Rolling back to image: ${{ steps.rollback-target.outputs.image }}"

        # Simulate rollback deployment (replace with actual rollback commands)
        echo "🔄 Pulling previous stable image..."
        docker pull ${{ steps.rollback-target.outputs.image }} || echo "⚠️ Previous image not found, manual intervention required"

        echo "🔄 Deploying rollback to ${{ steps.rollback-target.outputs.environment }}..."
        # Add your actual rollback deployment commands here

        echo "✅ Rollback completed for ${{ steps.rollback-target.outputs.target }}"

    - name: Verify rollback
      run: |
        echo "🏥 Verifying rollback deployment..."

        # Add health checks for rolled back deployment
        echo "Running post-rollback health checks..."
        sleep 5

        echo "✅ Rollback verification completed"

    - name: Notify rollback
      run: |
        echo "📢 Rollback notification"
        echo "Target: ${{ steps.rollback-target.outputs.target }}"
        echo "Image: ${{ steps.rollback-target.outputs.image }}"
        echo "Status: Completed"
        echo "Timestamp: $(date -u)"

        # Add notification to Slack, email, or other systems here
        echo "🚨 Manual review recommended for failed deployment"
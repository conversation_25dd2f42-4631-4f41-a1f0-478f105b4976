"""
Credentials Database Manager for Exchange Integration
Provides secure storage and retrieval of exchange API credentials
"""

import asyncio
import base64
import json
import logging
import os
from contextlib import asynccontextmanager
from typing import Any, Dict, List, Optional

import asyncpg
from cryptography.fernet import Fernet

from .database_config import DatabaseConfig

logger = logging.getLogger(__name__)


class CredentialsDatabase:
    """
    Direct PostgreSQL connection manager for exchange credentials.
    Provides secure, encrypted storage of API keys and secrets.
    """

    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.connected = False
        self._connection_lock = asyncio.Lock()
        self._encryption_key = self._get_or_create_encryption_key()
        self._cipher = Fernet(self._encryption_key)

    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key for credential storage."""
        key_env = os.getenv("CREDENTIALS_ENCRYPTION_KEY")
        if key_env:
            try:
                # <PERSON><PERSON><PERSON> expects the key as bytes (the base64-encoded string as bytes)
                return key_env.encode()
            except Exception:
                logger.warning(
                    "Invalid encryption key in environment, generating new one"
                )

        # Generate new key
        key = Fernet.generate_key()
        logger.warning(
            f"Generated new encryption key. Set CREDENTIALS_ENCRYPTION_KEY={key.decode()} in environment"
        )
        return key

    def _encrypt_data(self, data: str) -> str:
        """Encrypt sensitive data."""
        return self._cipher.encrypt(data.encode()).decode()

    def _decrypt_data(self, encrypted_data: str) -> str:
        """Decrypt sensitive data."""
        return self._cipher.decrypt(encrypted_data.encode()).decode()

    async def connect(self) -> bool:
        """Establish connection pool to PostgreSQL database."""
        async with self._connection_lock:
            if self.connected and self.pool:
                return True

            try:
                if self.pool:
                    await self.pool.close()

                config = DatabaseConfig()
                self.pool = await asyncpg.create_pool(
                    host=config.host,
                    port=config.port,
                    database=config.database,
                    user=config.user,
                    password=config.password,
                    min_size=1,
                    max_size=5,
                    command_timeout=30,  # Increase timeout for network latency
                    server_settings={
                        "application_name": "kamikaze_credentials",
                        "search_path": "public",
                    },
                )

                async with self.pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")

                self.connected = True
                logger.info(f"✅ Credentials database connected: {config.database}")
                return True

            except Exception as e:
                error_msg = str(e) if e else "Unknown connection error"
                logger.error(f"❌ Failed to connect credentials database: {error_msg}")
                logger.error(f"❌ Database config: host={config.host}, port={config.port}, database={config.database}")
                logger.error(f"❌ SSL mode: {config.ssl_mode}")
                logger.error(f"❌ Environment: {os.getenv('ENVIRONMENT', 'development')}")

                # Provide specific troubleshooting guidance
                if "authentication failed" in error_msg.lower():
                    logger.error("🔧 Authentication issue - check database credentials")
                elif "connection refused" in error_msg.lower():
                    logger.error("🔧 Connection refused - check database host/port and network access")
                elif "timeout" in error_msg.lower():
                    logger.error("🔧 Connection timeout - check network connectivity and security groups")
                elif "ssl" in error_msg.lower():
                    logger.error("🔧 SSL issue - check SSL configuration and certificates")

                self.connected = False
                self.pool = None
                return False

    async def disconnect(self):
        """Close connection pool."""
        async with self._connection_lock:
            if self.pool:
                await self.pool.close()
                self.pool = None
            self.connected = False
            logger.info("🔌 Credentials database disconnected")

    async def ensure_connected(self) -> bool:
        """Ensure database connection is available."""
        if not self.connected or not self.pool:
            return await self.connect()

        try:
            async with self.pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.warning(f"Credentials DB connection test failed, reconnecting: {e}")
            self.connected = False
            return await self.connect()

    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        if not await self.ensure_connected():
            raise ConnectionError("Failed to establish database connection")

        async with self.pool.acquire() as conn:
            yield conn

    # ============================================================================
    # Testnet Credentials Operations
    # ============================================================================







    # ============================================================================
    # Binance Live Credentials Operations
    # ============================================================================

    async def save_binance_credentials(
        self, user_id: int, api_key: str, secret_key: str
    ) -> bool:
        """Save or update Binance mainnet credentials for a user."""
        try:
            async with self.get_connection() as conn:


                # Encrypt sensitive data
                encrypted_api_key = self._encrypt_data(api_key)
                encrypted_secret_key = self._encrypt_data(secret_key)

                # Create masked versions for display
                api_key_masked = (
                    api_key[:8] + "..." + api_key[-4:]
                    if len(api_key) > 12
                    else "***"
                )
                secret_key_masked = (
                    secret_key[:8] + "..." + secret_key[-4:]
                    if len(secret_key) > 12
                    else "***"
                )

                # SECURITY FIX: Only store encrypted credentials, never plain text
                query = """
                    INSERT INTO binance_credentials (
                        user_id,
                        api_key_encrypted, api_secret_encrypted,
                        api_key_masked, api_secret_masked,
                        exchange, environment, is_mainnet,
                        is_active, can_trade, can_withdraw, can_deposit, account_type,
                        created_at, updated_at, last_used, last_validated
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, true, true, false, false, false, 'SPOT', NOW(), NOW(), NOW(), NOW())
                    ON CONFLICT (user_id)
                    DO UPDATE SET
                        api_key_encrypted = EXCLUDED.api_key_encrypted,
                        api_secret_encrypted = EXCLUDED.api_secret_encrypted,
                        api_key_masked = EXCLUDED.api_key_masked,
                        api_secret_masked = EXCLUDED.api_secret_masked,
                        is_mainnet = true,
                        is_active = true,
                        updated_at = NOW(),
                        last_used = NOW()
                """
                await conn.execute(
                    query,
                    user_id,
                    encrypted_api_key,
                    encrypted_secret_key,
                    api_key_masked,
                    secret_key_masked,
                    'binance',
                    'mainnet',
                )

                logger.info(
                    f"✅ Saved Binance mainnet credentials for user {user_id}"
                )
                return True

        except Exception as e:
            logger.error(
                f"❌ Failed to save Binance credentials for user {user_id}: {e}"
            )
            return False

    async def get_binance_credentials(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get Binance mainnet credentials for a user."""
        try:
            async with self.get_connection() as conn:
                query = """
                    SELECT api_key_encrypted, api_secret_encrypted
                    FROM binance_credentials
                    WHERE user_id = $1 AND is_mainnet = true AND is_active = true
                """
                row = await conn.fetchrow(query, user_id)

                if not row:
                    logger.warning(
                        f"No active mainnet Binance credentials found for user {user_id}"
                    )
                    return None

                # Decrypt and return credentials
                decrypted_api_key = self._decrypt_data(row["api_key_encrypted"])
                decrypted_secret_key = self._decrypt_data(row["api_secret_encrypted"])

                return {"api_key": decrypted_api_key, "secret_key": decrypted_secret_key}

        except Exception as e:
            logger.error(f"❌ Failed to get Binance credentials for user {user_id}: {e}")
            return None

    async def get_user_binance_credentials(self, user_id: int) -> Dict[str, Any]:
        """Get mainnet Binance credentials for a user."""
        try:
            async with self.get_connection() as conn:
                credentials = {"mainnet": None}

                # Check mainnet credentials only
                mainnet_query = """
                    SELECT id, user_id, exchange, environment, is_mainnet, is_active,
                           can_trade, can_withdraw, can_deposit, account_type,
                           created_at, updated_at, last_used, last_validated
                    FROM binance_credentials
                    WHERE user_id = $1 AND is_mainnet = true AND is_active = true
                """
                mainnet_result = await conn.fetchrow(mainnet_query, user_id)
                if mainnet_result:
                    credentials["mainnet"] = dict(mainnet_result)

                return credentials

        except Exception as e:
            logger.error(
                f"❌ Failed to get user Binance credentials for user {user_id}: {e}"
            )
            return {"mainnet": None}

    # ============================================================================
    # Credential Management Operations
    # ============================================================================

    async def deactivate_credentials(
        self, user_id: int, credential_type: str, exchange: str = None
    ) -> bool:
        """Deactivate credentials for a user."""
        try:
            async with self.get_connection() as conn:
                if credential_type == "testnet":
                    logger.error("Testnet credentials are no longer supported")
                    return False
                elif credential_type == "binance":
                    query = """
                        UPDATE binance_credentials
                        SET is_active = false, updated_at = NOW()
                        WHERE user_id = $1
                    """
                    await conn.execute(query, user_id)

                logger.info(
                    f"✅ Deactivated {credential_type} credentials for user {user_id}"
                )
                return True

        except Exception as e:
            logger.error(f"Failed to deactivate credentials: {e}")
            return False

    async def delete_credentials(
        self,
        user_id: int,
        credential_type: str,
        exchange: str = None,
        is_mainnet: bool = None,
    ) -> bool:
        """Permanently delete credentials for a user."""
        try:
            async with self.get_connection() as conn:
                if credential_type == "testnet":
                    logger.error("Testnet credentials are no longer supported")
                    return False
                elif credential_type == "binance":
                    if is_mainnet is not None:
                        query = "DELETE FROM binance_credentials WHERE user_id = $1 AND is_mainnet = $2"
                        await conn.execute(query, user_id, is_mainnet)
                    else:
                        query = "DELETE FROM binance_credentials WHERE user_id = $1"
                        await conn.execute(query, user_id)

                logger.info(
                    f"✅ Deleted {credential_type} credentials for user {user_id}"
                )
                return True

        except Exception as e:
            logger.error(f"Failed to delete credentials: {e}")
            return False


# Global credentials database instance
credentials_db = CredentialsDatabase()
"""
CloudWatch Logging Configuration for Kamikaze AI
Provides centralized logging to AWS CloudWatch Logs with no local file storage.
All logs are streamed directly to stdout/stderr for container runtime capture.
"""

import json
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Optional


class CloudWatchFormatter(logging.Formatter):
    """
    Custom formatter optimized for CloudWatch Logs.
    Outputs structured JSON logs for better parsing and filtering in CloudWatch.
    """
    
    def __init__(self):
        super().__init__()
        self.hostname = os.getenv('HOSTNAME', 'kamikaze-ai')
        self.environment = os.getenv('ENVIRONMENT', 'production')
        self.service_name = 'kamikaze-ai-backend'
        
    def format(self, record):
        """Format log record as structured JSON for CloudWatch."""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'service': self.service_name,
            'environment': self.environment,
            'hostname': self.hostname,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
            
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
            
        # Add module and function info for debugging
        if record.pathname:
            log_entry['module'] = record.module
            log_entry['function'] = record.funcName
            log_entry['line'] = record.lineno
            
        return json.dumps(log_entry, ensure_ascii=False)


class CloudWatchHandler(logging.StreamHandler):
    """
    Custom handler that sends logs to stdout/stderr for CloudWatch capture.
    ERROR and CRITICAL logs go to stderr, others to stdout.
    """
    
    def __init__(self):
        super().__init__()
        self.stdout_handler = logging.StreamHandler(sys.stdout)
        self.stderr_handler = logging.StreamHandler(sys.stderr)
        
    def emit(self, record):
        """Emit log record to appropriate stream."""
        try:
            if record.levelno >= logging.ERROR:
                self.stderr_handler.emit(record)
            else:
                self.stdout_handler.emit(record)
        except Exception:
            self.handleError(record)


def setup_cloudwatch_logging(
    component_name: str = "kamikaze-ai",
    log_level: str = None,
    structured_logs: bool = True
) -> logging.Logger:
    """
    Setup CloudWatch-optimized logging configuration.
    
    Args:
        component_name: Name of the component for logger naming
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        structured_logs: Whether to use structured JSON logging
        
    Returns:
        Configured logger instance optimized for CloudWatch
    """
    # Get log level from environment or parameter
    if log_level is None:
        log_level = os.getenv('LOG_LEVEL', 'INFO')
    
    # Create logger
    logger = logging.getLogger(component_name)
    
    # Clear any existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Set log level
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Prevent propagation to root logger to avoid duplicates
    logger.propagate = False
    
    # Create and configure handler
    if structured_logs:
        # Use structured JSON logging for CloudWatch
        handler = CloudWatchHandler()
        formatter = CloudWatchFormatter()
    else:
        # Use simple text logging
        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger


def setup_root_logging():
    """
    Setup root logging configuration for CloudWatch.
    This ensures all loggers in the application use CloudWatch-optimized settings.
    """
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # Set log level from environment
    log_level = os.getenv('LOG_LEVEL', 'INFO')
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Create CloudWatch handler
    handler = CloudWatchHandler()
    
    # Use structured logging if enabled
    use_structured_logs = os.getenv('STRUCTURED_LOGS', 'true').lower() == 'true'
    if use_structured_logs:
        formatter = CloudWatchFormatter()
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    handler.setFormatter(formatter)
    root_logger.addHandler(handler)
    
    # Disable file logging warnings
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('boto3').setLevel(logging.WARNING)
    logging.getLogger('botocore').setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger configured for CloudWatch.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Logger configured for CloudWatch
    """
    return logging.getLogger(name)


def log_with_context(logger: logging.Logger, level: str, message: str, **context):
    """
    Log a message with additional context fields for CloudWatch filtering.
    
    Args:
        logger: Logger instance
        level: Log level (info, warning, error, etc.)
        message: Log message
        **context: Additional context fields
    """
    # Create a log record with extra fields
    log_method = getattr(logger, level.lower())
    
    # Add context as extra fields
    extra = {'extra_fields': context}
    log_method(message, extra=extra)


# Environment-based configuration
def is_cloudwatch_enabled() -> bool:
    """Check if CloudWatch logging is enabled."""
    return os.getenv('USE_CLOUDWATCH_LOGS', 'true').lower() == 'true'


def is_file_logging_disabled() -> bool:
    """Check if file logging should be disabled."""
    return os.getenv('ENABLE_FILE_LOGGING', 'false').lower() == 'false'


# Initialize CloudWatch logging if enabled
if is_cloudwatch_enabled():
    setup_root_logging()


# Default logger for this module
logger = get_logger(__name__)

#!/bin/bash

# Setup CloudWatch Log Groups for Kamikaze AI
# This script creates the necessary CloudWatch log groups and sets retention policies

set -e

# Configuration
LOG_GROUP_NAME="/aws/kamikaze-ai/backend"
RETENTION_DAYS=30
AWS_REGION="${AWS_REGION:-us-east-1}"

echo "🔍 Setting up CloudWatch Log Groups for Kamikaze AI"
echo "=================================================="
echo "Log Group: ${LOG_GROUP_NAME}"
echo "Retention: ${RETENTION_DAYS} days"
echo "Region: ${AWS_REGION}"
echo ""

# Function to check if AWS CLI is available
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        echo "❌ AWS CLI is not installed or not in PATH"
        echo "Please install AWS CLI: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html"
        exit 1
    fi
}

# Function to check AWS credentials
check_aws_credentials() {
    echo "🔐 Checking AWS credentials..."
    if ! aws sts get-caller-identity &> /dev/null; then
        echo "❌ AWS credentials not configured or invalid"
        echo "Please configure AWS credentials using:"
        echo "  aws configure"
        echo "  or set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables"
        exit 1
    fi
    
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    USER_ARN=$(aws sts get-caller-identity --query Arn --output text)
    echo "✅ AWS credentials valid"
    echo "   Account: ***${ACCOUNT_ID: -2}"
    echo "   User: ${USER_ARN}"
    echo ""
}

# Function to create log group
create_log_group() {
    echo "📝 Creating CloudWatch log group: ${LOG_GROUP_NAME}"
    
    # Check if log group already exists
    if aws logs describe-log-groups --log-group-name-prefix "${LOG_GROUP_NAME}" --region "${AWS_REGION}" --query 'logGroups[?logGroupName==`'${LOG_GROUP_NAME}'`]' --output text | grep -q "${LOG_GROUP_NAME}"; then
        echo "✅ Log group already exists: ${LOG_GROUP_NAME}"
    else
        # Create the log group
        aws logs create-log-group \
            --log-group-name "${LOG_GROUP_NAME}" \
            --region "${AWS_REGION}"
        echo "✅ Created log group: ${LOG_GROUP_NAME}"
    fi
}

# Function to set retention policy
set_retention_policy() {
    echo "⏰ Setting retention policy to ${RETENTION_DAYS} days..."
    
    aws logs put-retention-policy \
        --log-group-name "${LOG_GROUP_NAME}" \
        --retention-in-days "${RETENTION_DAYS}" \
        --region "${AWS_REGION}"
    
    echo "✅ Retention policy set to ${RETENTION_DAYS} days"
}

# Function to create log streams (optional)
create_log_streams() {
    echo "📊 Creating log streams..."
    
    # Common log streams for the application
    STREAMS=(
        "application"
        "api"
        "trading"
        "database"
        "errors"
    )
    
    for stream in "${STREAMS[@]}"; do
        STREAM_NAME="${stream}-$(date +%Y%m%d)"
        
        # Check if stream exists
        if aws logs describe-log-streams \
            --log-group-name "${LOG_GROUP_NAME}" \
            --log-stream-name-prefix "${STREAM_NAME}" \
            --region "${AWS_REGION}" \
            --query 'logStreams[?logStreamName==`'${STREAM_NAME}'`]' \
            --output text | grep -q "${STREAM_NAME}"; then
            echo "   Stream exists: ${STREAM_NAME}"
        else
            # Create log stream
            aws logs create-log-stream \
                --log-group-name "${LOG_GROUP_NAME}" \
                --log-stream-name "${STREAM_NAME}" \
                --region "${AWS_REGION}"
            echo "   ✅ Created stream: ${STREAM_NAME}"
        fi
    done
}

# Function to display log group information
show_log_group_info() {
    echo ""
    echo "📋 CloudWatch Log Group Information"
    echo "=================================="
    
    aws logs describe-log-groups \
        --log-group-name-prefix "${LOG_GROUP_NAME}" \
        --region "${AWS_REGION}" \
        --query 'logGroups[0].[logGroupName,retentionInDays,storedBytes,creationTime]' \
        --output table
    
    echo ""
    echo "🔗 CloudWatch Console URL:"
    echo "https://${AWS_REGION}.console.aws.amazon.com/cloudwatch/home?region=${AWS_REGION}#logsV2:log-groups/log-group/$(echo ${LOG_GROUP_NAME} | sed 's/\//%2F/g')"
}

# Main execution
main() {
    echo "🚀 Starting CloudWatch setup..."
    
    check_aws_cli
    check_aws_credentials
    create_log_group
    set_retention_policy
    create_log_streams
    show_log_group_info
    
    echo ""
    echo "✅ CloudWatch Log Groups setup completed successfully!"
    echo ""
    echo "📝 Next steps:"
    echo "1. Deploy your application with CloudWatch logging enabled"
    echo "2. Monitor logs in the CloudWatch console"
    echo "3. Set up CloudWatch alarms and dashboards as needed"
    echo ""
    echo "🔧 Environment variables for your application:"
    echo "   USE_CLOUDWATCH_LOGS=true"
    echo "   ENABLE_FILE_LOGGING=false"
    echo "   STRUCTURED_LOGS=true"
    echo "   AWS_REGION=${AWS_REGION}"
}

# Handle command line arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "info")
        check_aws_cli
        check_aws_credentials
        show_log_group_info
        ;;
    "delete")
        echo "⚠️  Deleting CloudWatch log group: ${LOG_GROUP_NAME}"
        read -p "Are you sure? This will delete all logs! (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            aws logs delete-log-group --log-group-name "${LOG_GROUP_NAME}" --region "${AWS_REGION}"
            echo "✅ Log group deleted: ${LOG_GROUP_NAME}"
        else
            echo "❌ Deletion cancelled"
        fi
        ;;
    *)
        echo "Usage: $0 [setup|info|delete]"
        echo "  setup  - Create log groups and set retention (default)"
        echo "  info   - Show log group information"
        echo "  delete - Delete log group (with confirmation)"
        exit 1
        ;;
esac

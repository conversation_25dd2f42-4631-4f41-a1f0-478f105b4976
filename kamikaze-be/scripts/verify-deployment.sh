#!/bin/bash

# Deployment Verification Script
# This script helps diagnose deployment issues on the EC2 instance

set -e

echo "🔍 Kamikaze AI Deployment Verification"
echo "======================================"
echo "🔐 Security Note: All sensitive values are masked for security"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Docker container status
check_container_status() {
    log_info "Checking Docker container status..."
    
    if docker ps | grep -q kamikaze-ai; then
        log_info "✅ Container is running"
        docker ps | grep kamikaze-ai
    else
        log_error "❌ Container is not running"
        log_info "Checking stopped containers..."
        docker ps -a | grep kamikaze-ai || log_error "No kamikaze-ai container found"
        return 1
    fi
}

# Check container logs
check_container_logs() {
    log_info "Checking container logs for errors..."
    
    echo "--- Last 50 lines of container logs ---"
    docker logs --tail 50 kamikaze-ai
    echo "--- End of logs ---"
    
    # Check for specific error patterns
    if docker logs kamikaze-ai 2>&1 | grep -i "error\|failed\|exception"; then
        log_warn "⚠️ Errors found in container logs"
    else
        log_info "✅ No obvious errors in container logs"
    fi
}

# Check environment variables in container
check_container_env() {
    log_info "Checking container environment variables..."
    
    echo "--- AWS Configuration ---"
    docker exec kamikaze-ai env | grep -E "^AWS_" | sed 's/=.*/=***/g' || log_warn "No AWS environment variables found"

    echo "--- Database Configuration ---"
    docker exec kamikaze-ai env | grep -E "^DB_" | sed 's/\(DB_PASSWORD=\).*/\1***/g' | sed 's/\(DB_HOST=\).*/\1***/g' | sed 's/\(DB_USER=\).*/\1***/g' | sed 's/\(DB_NAME=\).*/\1***/g' | sed 's/\(DB_PORT=\).*/\1***/g' || log_warn "No DB environment variables found"

    echo "--- Application Configuration ---"
    docker exec kamikaze-ai env | grep -E "^(ENVIRONMENT|USE_AWS_SECRETS|LOG_LEVEL)" || log_warn "No app environment variables found"
}

# Test health endpoint
check_health_endpoint() {
    log_info "Testing health endpoint..."
    
    if curl -f -s http://localhost:8000/health > /tmp/health_response.json; then
        log_info "✅ Health endpoint is accessible"
        echo "--- Health Response ---"
        cat /tmp/health_response.json | python3 -m json.tool 2>/dev/null || cat /tmp/health_response.json
        echo "--- End Health Response ---"
    else
        log_error "❌ Health endpoint is not accessible"
        log_info "Checking if port 8000 is open..."
        ss -tlnp | grep :8000 || log_error "Port 8000 is not listening"
        return 1
    fi
}

# Test AWS connectivity from container
test_aws_connectivity() {
    log_info "Testing AWS connectivity from container..."

    # Test AWS STS (to verify credentials) - mask sensitive output
    STS_OUTPUT=$(docker exec kamikaze-ai aws sts get-caller-identity 2>/dev/null)
    if [ $? -eq 0 ]; then
        log_info "✅ AWS credentials are working"
        # Show only account (last 2 digits) and ARN structure without sensitive details
        echo "$STS_OUTPUT" | sed 's/"UserId": "[^"]*"/"UserId": "***masked***"/g' | sed 's/"Account": "[0-9]*\([0-9]\{2\}\)"/"Account": "***\1"/g'
    else
        log_error "❌ AWS credentials are not working"
        log_info "Testing AWS CLI availability..."
        docker exec kamikaze-ai which aws || log_warn "AWS CLI not found in container"
    fi

    # Test Secret Manager access - mask secret values
    log_info "Testing Secret Manager access..."

    # Test kmkz-app-secrets access (mask SecretString)
    APP_SECRET_OUTPUT=$(docker exec kamikaze-ai aws secretsmanager get-secret-value --secret-id kmkz-app-secrets --region us-east-1 2>/dev/null)
    if [ $? -eq 0 ]; then
        log_info "✅ kmkz-app-secrets is accessible"
        # Show metadata but completely mask the actual secret content
        echo "$APP_SECRET_OUTPUT" | sed 's/"SecretString": "[^"]*"/"SecretString": "***MASKED_FOR_SECURITY***"/g' | sed 's/gsk_[A-Za-z0-9_]*/***MASKED_API_KEY***/g' | sed 's/AKIA[A-Z0-9]*/***MASKED_ACCESS_KEY***/g'
    else
        log_error "❌ kmkz-app-secrets is not accessible"
    fi

    # Test kmkz-db-secrets access (mask SecretString)
    DB_SECRET_OUTPUT=$(docker exec kamikaze-ai aws secretsmanager get-secret-value --secret-id kmkz-db-secrets --region us-east-1 2>/dev/null)
    if [ $? -eq 0 ]; then
        log_info "✅ kmkz-db-secrets is accessible"
        # Show metadata but completely mask the actual secret content
        echo "$DB_SECRET_OUTPUT" | sed 's/"SecretString": "[^"]*"/"SecretString": "***MASKED_FOR_SECURITY***"/g'
    else
        log_error "❌ kmkz-db-secrets is not accessible"
    fi
}

# Test database connectivity
test_database_connectivity() {
    log_info "Testing database connectivity from container..."
    
    # Get database host from environment or health endpoint
    DB_HOST=$(docker exec kamikaze-ai env | grep "^DB_HOST=" | cut -d'=' -f2)
    DB_PORT=$(docker exec kamikaze-ai env | grep "^DB_PORT=" | cut -d'=' -f2 || echo "5432")
    
    if [ -n "$DB_HOST" ]; then
        log_info "Testing connection to $DB_HOST:$DB_PORT"
        if docker exec kamikaze-ai timeout 5 bash -c "echo > /dev/tcp/$DB_HOST/$DB_PORT" 2>/dev/null; then
            log_info "✅ Database host is reachable"
        else
            log_error "❌ Database host is not reachable"
        fi
    else
        log_warn "⚠️ DB_HOST not found in container environment"
    fi
}

# Main execution
main() {
    log_info "Starting deployment verification..."
    
    # Run all checks
    check_container_status || exit 1
    check_container_logs
    check_container_env
    check_health_endpoint || exit 1
    test_aws_connectivity
    test_database_connectivity
    
    log_info "✅ Deployment verification completed"
    log_info "Check the output above for any issues that need attention"
}

# Run main function
main "$@"
